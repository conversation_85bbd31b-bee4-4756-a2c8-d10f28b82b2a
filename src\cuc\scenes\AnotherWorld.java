package cuc.scenes;

import cuc.GameStarter;
import cuc.GameWorld;
import cuc.gameObjects.*;
import cuc.states.*;
import cuc.utils.*;
import javax.swing.ImageIcon;
import java.awt.Image;

public class AnotherWorld extends GameWorld {
    public AnotherWorld(GameStarter game) {
        super(game);
        initGameScene();
    }
    void initGameScene() {
        // 使用新的背景图片 yuhang_house_1.jpg
        bImage = new ImageIcon(getClass().getClassLoader().getResource("images/yuhang_house_1.jpg")).getImage();
        fImage = null;
        initAllParas();
        initAllGameObjects();
    }
    void initAllParas() {
        // 复制TaohuaIslandWorld的弹药、动画、音效参数，至少要有Avatar用到的
        allAnimParas.add(new AnimPara("土匪站立","土匪站立.png",1,8,new int[]{4,6,2,0,5,3,7,1})); 
        allAnimParas.add(new AnimPara("土匪行走","土匪行走.png",8,8,new int[]{4,6,2,0,5,3,7,1})); 
        allSoundParas.add(new SoundPara("弓箭发射声","弓箭发射.wav"));
        allSoundParas.add(new SoundPara("走路声","走路声.wav"));
        allAmmoParas.add(new AmmoPara("飞蛇箭",new Point2D(50,50),20,3000,30,getAnimParaByName("土匪行走"),getSoundParaByName("弓箭发射声"),null));
    }
    void initAllGameObjects() {
        // 添加一个Avatar对象
        Avatar avatar = new Avatar(this,new Point2D(200,200),new Point2D(128,152),100,getAmmoParaByName("飞蛇箭"));
        AnimPara stopAI = this.getAnimParaByName("土匪站立");
        Animation2D stopAnim = new Animation2D(stopAI);
        StopState stop = new StopState(avatar,1000000000,0,stopAnim,"");
        avatar.addState(stop);
        AnimPara moveAI = this.getAnimParaByName("土匪行走");
        Animation2D moveAnim = new Animation2D(moveAI);
        MoveState move = new MoveState(avatar,2000,8,moveAnim,"走路声.wav");
        avatar.addState(move);
        avatar.setCurState(stop);
        stop.start();
        this.addGameObject(avatar);
    }
} 