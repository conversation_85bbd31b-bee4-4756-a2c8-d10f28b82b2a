package cuc.gameObjects;

import java.awt.Graphics;
import java.awt.Image;

import javax.swing.ImageIcon;

import cuc.GameWorld;
import cuc.colliders.Collider;
import cuc.colliders.PolygonCollider;
import cuc.utils.Point2D;
/*
 * GameObject的一个子类，代表场景中的树木
 */
public class Tree extends GameObject{
	Image image;     //形象图
	public Tree(GameWorld game,Point2D pos,Point2D size,String imageFile){
		this.gameWorld = game;
		this.pos = pos;
		this.size = size;
		//为树木创建一个圆形碰撞盒
		//设置树木的碰撞盒半径
		float r = size.x*0.2f;   
		//设置树木的中心点
		Point2D center = new Point2D((int)(pos.x+r*0.1f),(int)(pos.y-r/2));  
		collider = new Collider(this,center,r);
		// 示例：如需不规则碰撞盒，可用如下代码
		// Point2D[] poly = new Point2D[] {
		//     new Point2D(pos.x-40, pos.y-100),
		//     new Point2D(pos.x+40, pos.y-100),
		//     new Point2D(pos.x+60, pos.y),
		//     new Point2D(pos.x-60, pos.y)
		// };
		// collider = new PolygonCollider(this, poly);
		image = new ImageIcon(getClass().getClassLoader().getResource("images/"+imageFile)).getImage();	
	}
	public void render(Graphics g){	
		//树木的渲染位置：树木的坐标位置是图片x方向的中点
		g.drawImage(image,pos.x-size.x/2,pos.y-size.y,size.x,size.y,null);
	}
}
