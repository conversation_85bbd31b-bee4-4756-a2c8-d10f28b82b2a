package cuc.states;

import cuc.gameObjects.Actor;
import cuc.gameObjects.Monster;
import cuc.utils.Animation2D;
import cuc.utils.Point2D;
import java.util.ArrayList;
import cuc.gameObjects.GameObject;

/*
 * 巡逻状态：
 * 怪物按照指定的路线来回行走
 * 当到达巡逻点时，自动转向下一个巡逻点
 */
public class PatrolState extends ActorState {
    private static final double ARRIVAL_THRESHOLD = 20.0; // 到达巡逻点的距离阈值
    
    public PatrolState(Actor host, float maxLifeTime, int speed, Animation2D animation, String soundFile) {
        super(host, maxLifeTime, speed, animation, soundFile);
        this.name = "patrol";
    }
    
    @Override
    public void update() {
        // 检查是否是Monster类型
        if (!(host instanceof Monster)) {
            super.update();
            return;
        }
        
        Monster monster = (Monster) host;
        
        // 检查是否到达当前巡逻目标点
        Point2D target = monster.getCurrentPatrolTarget();
        double distanceToTarget = Point2D.distance(monster.getPos(), target);
        
        if (distanceToTarget <= ARRIVAL_THRESHOLD) {
            // 到达巡逻点，移动到下一个巡逻点
            monster.moveToNextPatrolPoint();
            target = monster.getCurrentPatrolTarget();
        }
        
        // 计算朝向目标点的方向
        Point2D direction = new Point2D(target.x - monster.getPos().x, target.y - monster.getPos().y);
        int targetDirection = Point2D.getCloestDirection(animation.animPara.row, direction);
        setCurDirection(targetDirection);
        
        // 执行移动（带碰撞检测）
        move();
        
        // 播放动画
        animation.animate();
    }
    
    @Override
    public void move() {
        // 先进行碰撞检测
        ArrayList<GameObject> allGameObjects = host.gameWorld.getAllGameObjects();
        for (int i = 0; i < allGameObjects.size(); i++) {
            GameObject go = allGameObjects.get(i);
            if (this.host != go && this.host.collide(go)) {
                // 碰到障碍物，后退并尝试绕行
                this.back();
                // 尝试改变方向绕过障碍物
                tryAvoidObstacle();
                return;
            }
        }
        
        // 如果没有碰撞，继续正常移动
        super.move();
    }
    
    // 尝试避开障碍物
    private void tryAvoidObstacle() {
        if (!(host instanceof Monster)) return;
        
        Monster monster = (Monster) host;
        Point2D target = monster.getCurrentPatrolTarget();
        Point2D currentPos = monster.getPos();
        
        // 计算原始方向
        Point2D originalDirection = new Point2D(target.x - currentPos.x, target.y - currentPos.y);
        int originalDir = Point2D.getCloestDirection(animation.animPara.row, originalDirection);
        
        // 尝试左右偏移方向
        int animRow = animation.animPara.row;
        int[] tryDirections;
        
        if (animRow == 4) {
            // 4方向：尝试相邻方向
            tryDirections = new int[]{
                (originalDir + 1) % 4,
                (originalDir + 3) % 4  // 相当于 -1
            };
        } else {
            // 8方向：尝试相邻方向
            tryDirections = new int[]{
                (originalDir + 1) % 8,
                (originalDir + 7) % 8  // 相当于 -1
            };
        }
        
        // 尝试每个方向
        for (int dir : tryDirections) {
            setCurDirection(dir);
            // 模拟移动一步看是否会碰撞
            Point2D testPos = simulateMove(currentPos, dir);
            if (!wouldCollideAt(testPos)) {
                // 这个方向可行，使用这个方向
                super.move();
                return;
            }
        }
        
        // 如果所有方向都不行，就停在原地
    }
    
    // 模拟移动到指定方向的位置
    private Point2D simulateMove(Point2D currentPos, int direction) {
        int animRow = animation.animPara.row;
        Point2D newPos = new Point2D(currentPos.x, currentPos.y);
        
        if (animRow == 4) {
            if (direction == 0) {   // 左上运动
                newPos.x -= speedXY;
                newPos.y -= speedXY;
            } else if (direction == 1) {  // 右上运动
                newPos.x += speedXY;
                newPos.y -= speedXY;
            } else if (direction == 2) {  // 右下运动
                newPos.x += speedXY;
                newPos.y += speedXY;
            } else if (direction == 3) {  // 左下运动
                newPos.x -= speedXY;
                newPos.y += speedXY;
            }
        } else if (animRow == 8) {
            if (direction == 0) {   // 正上
                newPos.y -= speed;
            } else if (direction == 1) {  // 右上
                newPos.x += speedXY;
                newPos.y -= speedXY;
            } else if (direction == 2) {  // 右正
                newPos.x += speed;
            } else if (direction == 3) {  // 右下
                newPos.x += speedXY;
                newPos.y += speedXY;
            } else if (direction == 4) {  // 下正
                newPos.y += speed;
            } else if (direction == 5) {  // 左下
                newPos.x -= speedXY;
                newPos.y += speedXY;
            } else if (direction == 6) {  // 左正
                newPos.x -= speed;
            } else if (direction == 7) {  // 左上
                newPos.x -= speedXY;
                newPos.y -= speedXY;
            }
        }
        
        return newPos;
    }
    
    // 检查在指定位置是否会发生碰撞
    private boolean wouldCollideAt(Point2D testPos) {
        Point2D originalPos = host.getPos();
        host.setPos(testPos);
        
        ArrayList<GameObject> allGameObjects = host.gameWorld.getAllGameObjects();
        boolean collision = false;
        
        for (int i = 0; i < allGameObjects.size(); i++) {
            GameObject go = allGameObjects.get(i);
            if (host != go && host.collide(go)) {
                collision = true;
                break;
            }
        }
        
        // 恢复原始位置
        host.setPos(originalPos);
        return collision;
    }
}
