package cuc.gameObjects;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;
import cuc.GameWorld;
import cuc.utils.Point2D;

/*
 * 自定义对话框类：用于显示美观的对话框
 * 继承GameObject，可以像游戏对象一样统一管理
 * 支持多行文本、标题、图标等
 */
public class GDialog extends GameObject {
    private String title;             // 对话框标题
    private List<String> textLines;   // 对话框文本行
    private Font titleFont;           // 标题字体
    private Font textFont;            // 文本字体
    private Color titleColor;         // 标题颜色
    private Color textColor;          // 文本颜色
    private Color backgroundColor;    // 背景颜色
    private Color borderColor;        // 边框颜色
    private Color titleBarColor;      // 标题栏颜色
    private boolean visible;          // 是否可见
    private int padding;              // 内边距
    private int borderWidth;          // 边框宽度
    private int cornerRadius;         // 圆角半径
    private int lineSpacing;          // 行间距
    private boolean hasTitle;         // 是否有标题
    private boolean hasTitleBar;      // 是否有标题栏
    private Image icon;               // 对话框图标
    private int iconSize;             // 图标大小
    
    public GDialog(GameWorld game, Point2D pos, Point2D size, String title, String text) {
        this.gameWorld = game;
        this.pos = new Point2D(pos.x, pos.y);
        this.size = new Point2D(size.x, size.y);
        this.title = title;
        this.textLines = new ArrayList<>();
        
        // 将文本分行
        if (text != null) {
            wrapText(text);
        }
        
        // 默认样式设置
        this.titleFont = new Font("微软雅黑", Font.BOLD, 20);
        this.textFont = new Font("微软雅黑", Font.PLAIN, 16);
        this.titleColor = Color.WHITE;
        this.textColor = new Color(50, 50, 50);
        this.backgroundColor = new Color(250, 250, 250, 240); // 半透明白色
        this.borderColor = new Color(100, 100, 100);
        this.titleBarColor = new Color(70, 130, 180);         // 钢蓝色
        this.visible = true;
        this.padding = 15;
        this.borderWidth = 2;
        this.cornerRadius = 12;
        this.lineSpacing = 5;
        this.hasTitle = (title != null && !title.isEmpty());
        this.hasTitleBar = hasTitle;
        this.icon = null;
        this.iconSize = 24;
        
        // 不需要碰撞盒
        this.collider = null;
    }
    
    @Override
    public void render(Graphics g) {
        if (!visible) return;
        
        Graphics2D g2d = (Graphics2D) g;
        // 开启抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        
        int x = pos.x;
        int y = pos.y;
        int width = size.x;
        int height = size.y;
        
        // 绘制阴影
        g2d.setColor(new Color(0, 0, 0, 80));
        g2d.fillRoundRect(x + 5, y + 5, width, height, cornerRadius, cornerRadius);
        
        // 绘制主背景
        g2d.setColor(backgroundColor);
        g2d.fillRoundRect(x, y, width, height, cornerRadius, cornerRadius);
        
        // 绘制标题栏
        int titleBarHeight = 0;
        if (hasTitleBar && hasTitle) {
            titleBarHeight = 40;
            g2d.setColor(titleBarColor);
            g2d.fillRoundRect(x, y, width, titleBarHeight, cornerRadius, cornerRadius);
            // 填充标题栏下方的矩形部分
            g2d.fillRect(x, y + cornerRadius, width, titleBarHeight - cornerRadius);
            
            // 绘制标题文字
            g2d.setFont(titleFont);
            g2d.setColor(titleColor);
            FontMetrics fm = g2d.getFontMetrics();
            int titleWidth = fm.stringWidth(title);
            int titleX = x + padding;
            int titleY = y + (titleBarHeight - fm.getHeight()) / 2 + fm.getAscent();
            
            // 如果有图标，为图标留出空间
            if (icon != null) {
                g2d.drawImage(icon, x + padding, y + (titleBarHeight - iconSize) / 2, iconSize, iconSize, null);
                titleX += iconSize + 8;
            }
            
            g2d.drawString(title, titleX, titleY);
        }
        
        // 绘制边框
        g2d.setColor(borderColor);
        g2d.setStroke(new BasicStroke(borderWidth));
        g2d.drawRoundRect(x, y, width, height, cornerRadius, cornerRadius);
        
        // 绘制文本内容
        if (!textLines.isEmpty()) {
            g2d.setFont(textFont);
            g2d.setColor(textColor);
            FontMetrics fm = g2d.getFontMetrics();
            
            int textStartY = y + titleBarHeight + padding + fm.getAscent();
            int lineHeight = fm.getHeight() + lineSpacing;
            
            for (int i = 0; i < textLines.size(); i++) {
                String line = textLines.get(i);
                int textY = textStartY + i * lineHeight;
                g2d.drawString(line, x + padding, textY);
            }
        }
    }
    
    // 文本自动换行
    private void wrapText(String text) {
        textLines.clear();
        if (text == null || text.isEmpty()) return;
        
        // 简单的按字符数换行（可以改进为按像素宽度换行）
        int maxCharsPerLine = (size.x - 2 * padding) / 12; // 估算每行字符数
        
        String[] words = text.split(" ");
        StringBuilder currentLine = new StringBuilder();
        
        for (String word : words) {
            if (currentLine.length() + word.length() + 1 <= maxCharsPerLine) {
                if (currentLine.length() > 0) {
                    currentLine.append(" ");
                }
                currentLine.append(word);
            } else {
                if (currentLine.length() > 0) {
                    textLines.add(currentLine.toString());
                    currentLine = new StringBuilder(word);
                } else {
                    // 单词太长，强制换行
                    textLines.add(word);
                }
            }
        }
        
        if (currentLine.length() > 0) {
            textLines.add(currentLine.toString());
        }
    }
    
    // Getter和Setter方法
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
        this.hasTitle = (title != null && !title.isEmpty());
    }
    
    public void setText(String text) {
        wrapText(text);
    }
    
    public void addTextLine(String line) {
        textLines.add(line);
    }
    
    public void clearText() {
        textLines.clear();
    }
    
    public boolean isVisible() {
        return visible;
    }
    
    public void setVisible(boolean visible) {
        this.visible = visible;
    }
    
    public void setIcon(Image icon) {
        this.icon = icon;
    }
    
    public void setColors(Color backgroundColor, Color borderColor, Color titleBarColor) {
        this.backgroundColor = backgroundColor;
        this.borderColor = borderColor;
        this.titleBarColor = titleBarColor;
    }
    
    public void setTextColors(Color titleColor, Color textColor) {
        this.titleColor = titleColor;
        this.textColor = textColor;
    }
    
    public void setFonts(Font titleFont, Font textFont) {
        this.titleFont = titleFont;
        this.textFont = textFont;
    }
    
    public void setHasTitleBar(boolean hasTitleBar) {
        this.hasTitleBar = hasTitleBar;
    }
    
    // 预设样式
    public void setInfoStyle() {
        setColors(
            new Color(240, 248, 255, 240),  // 淡蓝色背景
            new Color(70, 130, 180),        // 蓝色边框
            new Color(70, 130, 180)         // 蓝色标题栏
        );
        setTextColors(Color.WHITE, new Color(25, 25, 112));
    }
    
    public void setWarningStyle() {
        setColors(
            new Color(255, 250, 205, 240),  // 淡黄色背景
            new Color(255, 140, 0),         // 橙色边框
            new Color(255, 140, 0)          // 橙色标题栏
        );
        setTextColors(Color.WHITE, new Color(139, 69, 19));
    }
    
    public void setErrorStyle() {
        setColors(
            new Color(255, 240, 240, 240),  // 淡红色背景
            new Color(220, 20, 60),         // 红色边框
            new Color(220, 20, 60)          // 红色标题栏
        );
        setTextColors(Color.WHITE, new Color(139, 0, 0));
    }
    
    public void setSuccessStyle() {
        setColors(
            new Color(240, 255, 240, 240),  // 淡绿色背景
            new Color(60, 179, 113),        // 绿色边框
            new Color(60, 179, 113)         // 绿色标题栏
        );
        setTextColors(Color.WHITE, new Color(0, 100, 0));
    }
}
