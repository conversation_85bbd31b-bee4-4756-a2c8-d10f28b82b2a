package cuc.gameObjects;

import java.util.Random;

import cuc.GameWorld;
import cuc.states.ActorState;
import cuc.utils.AmmoPara;
import cuc.utils.Point2D;
import cuc.states.DialogState;
import cuc.states.TradeState;
import java.awt.event.KeyEvent;
import java.awt.event.MouseEvent;
import cuc.utils.AnimPara;
import cuc.utils.Animation2D;

/*
 * 创建一个Actor的子类：货郎类。
 * 这是一个NPC，这里定义这个NPC的行为：
 * 无人时随机游走，有人时停下来面向来人
 */

public class Vender extends Actor{
	Avatar avatar;    //货郎需要知道阿凡达在哪里
	
	public int range;   //货郎的反应范围：当阿凡达进入这个范围时做出反应
	
	public Vender(GameWorld game, Point2D pos, Point2D size, int life, AmmoPara ammoPara) {
		super(game, pos, size, life, ammoPara);

		avatar = gameWorld.getAvatar();   //向游戏世界获取阿凡达对象
		range = 300;       //设置反应距离
	}
	//重写状态更新函数，添加货郎独特的行为逻辑
    public void update(){
        //检查阿凡达是否靠近自己
        double distanceToAvatar = Point2D.distance(this.pos, avatar.pos);

        if( distanceToAvatar <= range ){
            // 玩家在范围内
            if(!(this.curState instanceof DialogState) && !(this.curState instanceof TradeState)){
                // 停下来并面向玩家
                int direction = Point2D.getCloestDirection(curState.animation.animPara.row,new Point2D(avatar.pos.x-pos.x,avatar.pos.y-pos.y));
                curState.setCurDirection(direction);
                // 切换到对话状态
                AnimPara anim = curState.animation.animPara;
                Animation2D anim2d = new Animation2D(anim);
                DialogState dialog = new DialogState(this, 100000, 0, anim2d, "你好，需要买点什么吗？(E键交易)");
                this.addState(dialog); // 先加入状态池
                this.switchState("dialog"); // 再切换
            } else if(this.curState instanceof DialogState) {
                // 在对话状态中持续面向玩家
                int direction = Point2D.getCloestDirection(curState.animation.animPara.row,new Point2D(avatar.pos.x-pos.x,avatar.pos.y-pos.y));
                curState.setCurDirection(direction);
            }
        } else {
            // 玩家离开范围，退出对话和交易状态，回到正常闲逛
            if(this.curState instanceof DialogState || this.curState instanceof TradeState){
                this.switchState("stop"); // 回到停止状态，会触发onStateFinish继续闲逛
            }
        }
        //转发给当前状态进行更新
        if(curState != null) curState.update();
    }

    // 处理键盘事件，E键进入交易
    public void keyPressed(int key) {
        if(curState instanceof DialogState && key == KeyEvent.VK_E) {
            // 先停止当前对话状态，清理界面
            curState.stop();
            
            AnimPara anim = curState.animation.animPara;
            Animation2D anim2d = new Animation2D(anim);
            TradeState trade = new TradeState(this, 100000, 0, anim2d);
            this.addState(trade);
            this.switchState("trade");
        }
    }
    // 处理鼠标事件，转发给TradeState
    public void mouseReleased(MouseEvent e) {
        if(curState instanceof TradeState) {
            ((TradeState)curState).mouseReleased(e);
        }
    }
    /*
	 * 重写这个状态到期后的回调函数
	 * 让游戏角色负责决定下一个状态（而不是由状态自己决定）
	 */
	public void onStateFinish(ActorState state){		
		//当货郎的静止状态结束后，随机选择一个方向，让货郎转入行走状态
		if(state.name == "stop"){
			// 生成一个在[0, animRow)范围内的随机整数
			Random random = new Random();				
			int randomNumber = random.nextInt(animRow);
			//转向随机的新方向	
			this.curState.setCurDirection(randomNumber);		
			this.switchState("move");	//转向行走状态		
		}else if(state.name == "move" )	{   //如果结束的状态是行走状态，则转向静止状态			
			this.switchState("stop");
		}
	}

    // switchState防御性修正（建议放在Actor.java里，但此处补充防御）
    @Override
    public void switchState(String nextState){
        cuc.states.ActorState next = getStateByName(nextState);
        if (next == null) {
            System.out.println("切换状态失败，未找到状态：" + nextState);
            return;
        }
        next.setCurDirection(curState.getCurDirection());
        curState.stop();
        curState = next;
        curState.start();
    }
}
