package cuc.states;

import cuc.gameObjects.Actor;
import cuc.gameObjects.Monster;
import cuc.gameObjects.Avatar;
import cuc.utils.Animation2D;
import cuc.utils.Point2D;
import java.util.ArrayList;
import cuc.gameObjects.GameObject;

/*
 * 逃跑状态：
 * 当怪物生命值过低时，逃离阿凡达
 * 会朝着远离阿凡达的方向快速移动
 */
public class EscapeState extends ActorState {
    private static final double SAFE_DISTANCE = 400.0; // 安全距离
    
    public EscapeState(Actor host, float maxLifeTime, int speed, Animation2D animation, String soundFile) {
        super(host, maxLifeTime, speed, animation, soundFile);
        this.name = "escape";
    }
    
    @Override
    public void update() {
        // 检查是否是Monster类型
        if (!(host instanceof Monster)) {
            super.update();
            return;
        }
        
        Monster monster = (Monster) host;
        Avatar avatar = monster.avatar;
        
        if (avatar == null) {
            // 没有阿凡达，回到巡逻状态
            host.onStateFinish(this);
            return;
        }
        
        double distanceToAvatar = Point2D.distance(monster.getPos(), avatar.getPos());
        
        // 如果已经逃到安全距离，检查是否可以恢复
        if (distanceToAvatar >= SAFE_DISTANCE) {
            float lifeRatio = (float) monster.life / monster.maxLife;
            if (lifeRatio > monster.escapeThreshold + 0.1f) { // 增加一点缓冲，避免频繁切换
                // 生命值恢复了一些，可以回到巡逻状态
                host.onStateFinish(this);
                return;
            }
        }
        
        // 计算远离阿凡达的方向
        Point2D escapeDirection = calculateEscapeDirection(monster, avatar);
        int targetDirection = Point2D.getCloestDirection(animation.animPara.row, escapeDirection);
        setCurDirection(targetDirection);
        
        // 执行逃跑移动
        escapeMove(monster, avatar);
        
        // 播放动画
        animation.animate();
    }
    
    // 计算逃跑方向（远离阿凡达）
    private Point2D calculateEscapeDirection(Monster monster, Avatar avatar) {
        Point2D monsterPos = monster.getPos();
        Point2D avatarPos = avatar.getPos();
        
        // 计算从阿凡达指向怪物的向量（逃跑方向）
        int dx = monsterPos.x - avatarPos.x;
        int dy = monsterPos.y - avatarPos.y;
        
        // 如果距离太近，随机选择一个方向
        if (Math.abs(dx) < 10 && Math.abs(dy) < 10) {
            dx = (Math.random() > 0.5) ? 100 : -100;
            dy = (Math.random() > 0.5) ? 100 : -100;
        }
        
        return new Point2D(dx, dy);
    }
    
    // 逃跑移动
    private void escapeMove(Monster monster, Avatar avatar) {
        // 先进行碰撞检测
        ArrayList<GameObject> allGameObjects = host.gameWorld.getAllGameObjects();
        for (int i = 0; i < allGameObjects.size(); i++) {
            GameObject go = allGameObjects.get(i);
            if (host != go && go != avatar && host.collide(go)) {
                // 碰到障碍物，尝试绕行
                this.back();
                tryAvoidObstacle(monster, avatar);
                return;
            }
        }
        
        // 如果没有碰撞，继续逃跑
        super.move();
    }
    
    // 尝试避开障碍物继续逃跑
    private void tryAvoidObstacle(Monster monster, Avatar avatar) {
        Point2D escapeDirection = calculateEscapeDirection(monster, avatar);
        int originalDir = Point2D.getCloestDirection(animation.animPara.row, escapeDirection);
        
        // 尝试左右偏移方向
        int animRow = animation.animPara.row;
        int[] tryDirections;
        
        if (animRow == 4) {
            // 4方向：尝试所有其他方向，优先选择远离阿凡达的方向
            tryDirections = new int[]{
                (originalDir + 1) % 4,
                (originalDir + 3) % 4,  // 相当于 -1
                (originalDir + 2) % 4   // 相反方向作为最后选择
            };
        } else {
            // 8方向：尝试相邻方向
            tryDirections = new int[]{
                (originalDir + 1) % 8,
                (originalDir + 7) % 8,  // 相当于 -1
                (originalDir + 2) % 8,
                (originalDir + 6) % 8,  // 相当于 -2
                (originalDir + 4) % 8   // 相反方向作为最后选择
            };
        }
        
        // 尝试每个方向
        for (int dir : tryDirections) {
            setCurDirection(dir);
            // 模拟移动一步看是否会碰撞
            Point2D testPos = simulateMove(monster.getPos(), dir);
            if (!wouldCollideAt(testPos, avatar)) {
                // 这个方向可行，使用这个方向
                super.move();
                return;
            }
        }
        
        // 如果所有方向都不行，就停在原地（但这种情况应该很少见）
    }
    
    // 模拟移动到指定方向的位置
    private Point2D simulateMove(Point2D currentPos, int direction) {
        int animRow = animation.animPara.row;
        Point2D newPos = new Point2D(currentPos.x, currentPos.y);
        
        if (animRow == 4) {
            if (direction == 0) {   // 左上运动
                newPos.x -= speedXY;
                newPos.y -= speedXY;
            } else if (direction == 1) {  // 右上运动
                newPos.x += speedXY;
                newPos.y -= speedXY;
            } else if (direction == 2) {  // 右下运动
                newPos.x += speedXY;
                newPos.y += speedXY;
            } else if (direction == 3) {  // 左下运动
                newPos.x -= speedXY;
                newPos.y += speedXY;
            }
        } else if (animRow == 8) {
            if (direction == 0) {   // 正上
                newPos.y -= speed;
            } else if (direction == 1) {  // 右上
                newPos.x += speedXY;
                newPos.y -= speedXY;
            } else if (direction == 2) {  // 右正
                newPos.x += speed;
            } else if (direction == 3) {  // 右下
                newPos.x += speedXY;
                newPos.y += speedXY;
            } else if (direction == 4) {  // 下正
                newPos.y += speed;
            } else if (direction == 5) {  // 左下
                newPos.x -= speedXY;
                newPos.y += speedXY;
            } else if (direction == 6) {  // 左正
                newPos.x -= speed;
            } else if (direction == 7) {  // 左上
                newPos.x -= speedXY;
                newPos.y -= speedXY;
            }
        }
        
        return newPos;
    }
    
    // 检查在指定位置是否会发生碰撞（排除阿凡达）
    private boolean wouldCollideAt(Point2D testPos, Avatar avatar) {
        Point2D originalPos = host.getPos();
        host.setPos(testPos);
        
        ArrayList<GameObject> allGameObjects = host.gameWorld.getAllGameObjects();
        boolean collision = false;
        
        for (int i = 0; i < allGameObjects.size(); i++) {
            GameObject go = allGameObjects.get(i);
            if (host != go && go != avatar && host.collide(go)) {
                collision = true;
                break;
            }
        }
        
        // 恢复原始位置
        host.setPos(originalPos);
        return collision;
    }
}
