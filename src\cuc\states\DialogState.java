package cuc.states;

import cuc.gameObjects.Actor;
import cuc.gameObjects.GDialog;
import cuc.utils.Animation2D;
import cuc.utils.Point2D;
import java.awt.Graphics;

public class DialogState extends ActorState {
    private GDialog dialog;

    public DialogState(Actor host, float maxLifeTime, int speed, Animation2D animation, String dialogText) {
        super(host, maxLifeTime, speed, animation, "");
        this.name = "dialog";

        // 创建美观的对话框
        int dialogWidth = 300;
        int dialogHeight = 80;
        int x = host.getPos().x - dialogWidth/2;
        int y = host.getPos().y - host.getSize().y/2 - dialogHeight - 20;

        this.dialog = new GDialog(host.gameWorld, new Point2D(x, y), new Point2D(dialogWidth, dialogHeight),
                                 "NPC对话", dialogText);
        this.dialog.setInfoStyle();
        this.dialog.setHasTitleBar(false); // 不显示标题栏，更简洁

        // 添加到游戏世界中
        host.gameWorld.addGameObject(dialog);
    }

    @Override
    public void render(Graphics g) {
        super.render(g);

        // 更新对话框位置，跟随角色
        if (dialog != null) {
            int dialogWidth = 300;
            int dialogHeight = 80;
            int x = host.getPos().x - dialogWidth/2;
            int y = host.getPos().y - host.getSize().y/2 - dialogHeight - 20;
            dialog.setPos(new Point2D(x, y));
        }
    }

    @Override
    public void stop() {
        super.stop();
        // 移除对话框
        if (dialog != null) {
            host.gameWorld.removeGameObject(dialog);
            dialog = null;
        }
    }
}