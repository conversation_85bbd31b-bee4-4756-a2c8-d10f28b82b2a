package cuc.states;

import cuc.gameObjects.Actor;
import cuc.utils.Animation2D;
import java.awt.Graphics;

public class DialogState extends ActorState {
    private String dialogText;
    public DialogState(Actor host, float maxLifeTime, int speed, Animation2D animation, String dialogText) {
        super(host, maxLifeTime, speed, animation, "");
        this.name = "dialog";
        this.dialogText = dialogText;
    }
    @Override
    public void render(Graphics g) {
        super.render(g);
        // 在角色上方显示对话文本
        int x = host.getPos().x;
        int y = host.getPos().y - host.getSize().y/2 - 30;
        g.setColor(java.awt.Color.WHITE);
        g.fillRect(x-60, y-30, 120, 30);
        g.setColor(java.awt.Color.BLACK);
        g.drawRect(x-60, y-30, 120, 30);
        g.drawString(dialogText, x-55, y-10);
    }
} 