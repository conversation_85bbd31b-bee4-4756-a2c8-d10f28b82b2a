package cuc.colliders;

import cuc.gameObjects.GameObject;
import cuc.utils.Point2D;

public class PolygonCollider extends Collider {
    public Point2D[] vertices; // 多边形顶点，按顺时针或逆时针排列

    public PolygonCollider(GameObject host, Point2D[] vertices) {
        super(host, null, 0); // center/radius无意义
        this.vertices = vertices;
    }

    // 判断点是否在多边形内（射线法）
    public boolean contains(Point2D p) {
        int n = vertices.length;
        int count = 0;
        for (int i = 0; i < n; i++) {
            Point2D a = vertices[i];
            Point2D b = vertices[(i + 1) % n];
            if (((a.y > p.y) != (b.y > p.y)) &&
                (p.x < (b.x - a.x) * (p.y - a.y) / (b.y - a.y + 0.00001) + a.x)) {
                count++;
            }
        }
        return count % 2 == 1;
    }

    // 圆-多边形碰撞检测
    public boolean collideCircle(Point2D center, float radius) {
        // 1. 圆心在多边形内
        if (contains(center)) return true;
        // 2. 圆与多边形任意一条边有交点
        int n = vertices.length;
        for (int i = 0; i < n; i++) {
            Point2D a = vertices[i];
            Point2D b = vertices[(i + 1) % n];
            if (circleLineCollide(center, radius, a, b)) return true;
        }
        return false;
    }

    // 圆与线段碰撞
    private boolean circleLineCollide(Point2D c, float r, Point2D a, Point2D b) {
        // 最近点投影
        float dx = b.x - a.x;
        float dy = b.y - a.y;
        float fx = c.x - a.x;
        float fy = c.y - a.y;
        float t = (fx * dx + fy * dy) / (dx * dx + dy * dy);
        t = Math.max(0, Math.min(1, t));
        float px = a.x + t * dx;
        float py = a.y + t * dy;
        float dist = (float)Math.sqrt((c.x - px) * (c.x - px) + (c.y - py) * (c.y - py));
        return dist <= r;
    }

    // 多边形-多边形碰撞（可选，暂不实现）
    // ...

    // 重写collide方法
    @Override
    public boolean collide(Collider another) {
        if (another instanceof PolygonCollider) {
            // 多边形-多边形碰撞（可选）
            return false; // 可扩展
        } else {
            // 圆-多边形
            return collideCircle(another.center, another.radius);
        }
    }
} 