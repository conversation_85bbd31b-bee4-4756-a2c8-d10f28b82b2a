package cuc.states;

import cuc.gameObjects.Actor;
import cuc.gameObjects.Monster;
import cuc.gameObjects.Avatar;
import cuc.utils.Animation2D;
import cuc.utils.Point2D;
import java.util.ArrayList;
import cuc.gameObjects.GameObject;

/*
 * 战斗状态：
 * 怪物向阿凡达发动攻击
 * 会朝向阿凡达并定期发射弹药
 */
public class FightState extends ActorState {
    private long lastAttackTime;     // 上次攻击时间
    private static final long ATTACK_INTERVAL = 1500; // 攻击间隔（毫秒）
    private static final double ATTACK_RANGE = 150.0;  // 攻击范围
    
    public FightState(Actor host, float maxLifeTime, int speed, Animation2D animation, String soundFile) {
        super(host, maxLifeTime, speed, animation, soundFile);
        this.name = "fight";
        this.lastAttackTime = 0;
    }
    
    @Override
    public void update() {
        // 检查是否是Monster类型
        if (!(host instanceof Monster)) {
            super.update();
            return;
        }
        
        Monster monster = (Monster) host;
        Avatar avatar = monster.avatar;
        
        if (avatar == null) {
            // 没有阿凡达，回到巡逻状态
            host.onStateFinish(this);
            return;
        }
        
        double distanceToAvatar = Point2D.distance(monster.getPos(), avatar.getPos());
        
        // 始终面向阿凡达
        Point2D direction = new Point2D(avatar.getPos().x - monster.getPos().x, 
                                       avatar.getPos().y - monster.getPos().y);
        int targetDirection = Point2D.getCloestDirection(animation.animPara.row, direction);
        setCurDirection(targetDirection);
        
        // 如果距离太远，向阿凡达移动
        if (distanceToAvatar > ATTACK_RANGE) {
            moveTowardsAvatar(monster, avatar);
        } else {
            // 在攻击范围内，进行攻击
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastAttackTime >= ATTACK_INTERVAL) {
                attackAvatar(monster, avatar);
                lastAttackTime = currentTime;
            }
        }
        
        // 播放动画
        animation.animate();
    }
    
    // 向阿凡达移动
    private void moveTowardsAvatar(Monster monster, Avatar avatar) {
        // 先进行碰撞检测
        ArrayList<GameObject> allGameObjects = host.gameWorld.getAllGameObjects();
        for (int i = 0; i < allGameObjects.size(); i++) {
            GameObject go = allGameObjects.get(i);
            if (host != go && go != avatar && host.collide(go)) {
                // 碰到障碍物，尝试绕行
                this.back();
                tryAvoidObstacle(monster, avatar);
                return;
            }
        }
        
        // 如果没有碰撞，继续朝向阿凡达移动
        super.move();
    }
    
    // 攻击阿凡达
    private void attackAvatar(Monster monster, Avatar avatar) {
        // 发射弹药攻击阿凡达
        Point2D targetPos = new Point2D(avatar.getPos().x, avatar.getPos().y);
        monster.shoot(targetPos);
    }
    
    // 尝试避开障碍物朝向阿凡达
    private void tryAvoidObstacle(Monster monster, Avatar avatar) {
        Point2D avatarPos = avatar.getPos();
        Point2D currentPos = monster.getPos();
        
        // 计算朝向阿凡达的原始方向
        Point2D originalDirection = new Point2D(avatarPos.x - currentPos.x, avatarPos.y - currentPos.y);
        int originalDir = Point2D.getCloestDirection(animation.animPara.row, originalDirection);
        
        // 尝试左右偏移方向
        int animRow = animation.animPara.row;
        int[] tryDirections;
        
        if (animRow == 4) {
            // 4方向：尝试相邻方向
            tryDirections = new int[]{
                (originalDir + 1) % 4,
                (originalDir + 3) % 4  // 相当于 -1
            };
        } else {
            // 8方向：尝试相邻方向
            tryDirections = new int[]{
                (originalDir + 1) % 8,
                (originalDir + 7) % 8  // 相当于 -1
            };
        }
        
        // 尝试每个方向
        for (int dir : tryDirections) {
            setCurDirection(dir);
            // 模拟移动一步看是否会碰撞
            Point2D testPos = simulateMove(currentPos, dir);
            if (!wouldCollideAt(testPos, avatar)) {
                // 这个方向可行，使用这个方向
                super.move();
                return;
            }
        }
        
        // 如果所有方向都不行，就停在原地继续攻击
    }
    
    // 模拟移动到指定方向的位置
    private Point2D simulateMove(Point2D currentPos, int direction) {
        int animRow = animation.animPara.row;
        Point2D newPos = new Point2D(currentPos.x, currentPos.y);
        
        if (animRow == 4) {
            if (direction == 0) {   // 左上运动
                newPos.x -= speedXY;
                newPos.y -= speedXY;
            } else if (direction == 1) {  // 右上运动
                newPos.x += speedXY;
                newPos.y -= speedXY;
            } else if (direction == 2) {  // 右下运动
                newPos.x += speedXY;
                newPos.y += speedXY;
            } else if (direction == 3) {  // 左下运动
                newPos.x -= speedXY;
                newPos.y += speedXY;
            }
        } else if (animRow == 8) {
            if (direction == 0) {   // 正上
                newPos.y -= speed;
            } else if (direction == 1) {  // 右上
                newPos.x += speedXY;
                newPos.y -= speedXY;
            } else if (direction == 2) {  // 右正
                newPos.x += speed;
            } else if (direction == 3) {  // 右下
                newPos.x += speedXY;
                newPos.y += speedXY;
            } else if (direction == 4) {  // 下正
                newPos.y += speed;
            } else if (direction == 5) {  // 左下
                newPos.x -= speedXY;
                newPos.y += speedXY;
            } else if (direction == 6) {  // 左正
                newPos.x -= speed;
            } else if (direction == 7) {  // 左上
                newPos.x -= speedXY;
                newPos.y -= speedXY;
            }
        }
        
        return newPos;
    }
    
    // 检查在指定位置是否会发生碰撞（排除阿凡达）
    private boolean wouldCollideAt(Point2D testPos, Avatar avatar) {
        Point2D originalPos = host.getPos();
        host.setPos(testPos);
        
        ArrayList<GameObject> allGameObjects = host.gameWorld.getAllGameObjects();
        boolean collision = false;
        
        for (int i = 0; i < allGameObjects.size(); i++) {
            GameObject go = allGameObjects.get(i);
            if (host != go && go != avatar && host.collide(go)) {
                collision = true;
                break;
            }
        }
        
        // 恢复原始位置
        host.setPos(originalPos);
        return collision;
    }
}
