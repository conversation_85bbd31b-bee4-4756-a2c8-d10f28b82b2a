package cuc.gameObjects;

import java.util.ArrayList;
import cuc.GameWorld;
import cuc.states.ActorState;
import cuc.states.PatrolState;
import cuc.states.FightState;
import cuc.states.EscapeState;
import cuc.utils.AmmoPara;
import cuc.utils.Point2D;
import cuc.utils.AnimPara;
import cuc.utils.Animation2D;

/*
 * 创建一个Actor的子类：怪物类。
 * 这是一个智能NPC，具有以下行为：
 * 1. 巡逻：按照指定的路线来回行走
 * 2. 战斗：当阿凡达靠近时，向阿凡达发动攻击
 * 3. 逃跑：当生命值掉到一定程度后，逃离阿凡达
 */
public class Monster extends Actor {
    public Avatar avatar;       // 怪物需要知道阿凡达在哪里
    public int detectionRange;  // 怪物的检测范围：当阿凡达进入这个范围时开始战斗
    public int escapeRange;     // 怪物的逃跑范围：逃跑时与阿凡达保持的距离
    public int maxLife;         // 怪物的最大生命值
    public float escapeThreshold; // 逃跑阈值：生命值低于此比例时开始逃跑
    
    // 巡逻路径点
    public ArrayList<Point2D> patrolPoints;
    public int currentPatrolIndex; // 当前巡逻目标点索引
    public boolean patrolForward;  // 巡逻方向：true为正向，false为反向
    
    public Monster(GameWorld game, Point2D pos, Point2D size, int life, AmmoPara ammoPara) {
        super(game, pos, size, life, ammoPara);
        
        avatar = gameWorld.getAvatar();   // 向游戏世界获取阿凡达对象
        detectionRange = 200;             // 设置检测距离
        escapeRange = 300;                // 设置逃跑距离
        maxLife = life;                   // 记录最大生命值
        escapeThreshold = 0.3f;           // 生命值低于30%时逃跑
        
        // 初始化巡逻路径（默认路径，可以通过方法设置）
        patrolPoints = new ArrayList<Point2D>();
        patrolPoints.add(new Point2D(pos.x - 100, pos.y));
        patrolPoints.add(new Point2D(pos.x + 100, pos.y));
        currentPatrolIndex = 0;
        patrolForward = true;
    }
    
    // 设置巡逻路径
    public void setPatrolPath(ArrayList<Point2D> points) {
        this.patrolPoints = points;
        this.currentPatrolIndex = 0;
        this.patrolForward = true;
    }
    
    // 获取当前巡逻目标点
    public Point2D getCurrentPatrolTarget() {
        if (patrolPoints.isEmpty()) return pos;
        return patrolPoints.get(currentPatrolIndex);
    }
    
    // 移动到下一个巡逻点
    public void moveToNextPatrolPoint() {
        if (patrolPoints.size() <= 1) return;
        
        if (patrolForward) {
            currentPatrolIndex++;
            if (currentPatrolIndex >= patrolPoints.size()) {
                currentPatrolIndex = patrolPoints.size() - 2;
                patrolForward = false;
            }
        } else {
            currentPatrolIndex--;
            if (currentPatrolIndex < 0) {
                currentPatrolIndex = 1;
                patrolForward = true;
            }
        }
    }
    
    // 重写状态更新函数，添加怪物独特的行为逻辑
    public void update() {
        // 检查阿凡达是否存在
        if (avatar == null) {
            avatar = gameWorld.getAvatar();
            if (avatar == null) {
                // 如果没有阿凡达，继续巡逻
                if (!(this.curState instanceof PatrolState)) {
                    switchToPatrol();
                }
                if (curState != null) curState.update();
                return;
            }
        }
        
        double distanceToAvatar = Point2D.distance(this.pos, avatar.pos);
        float lifeRatio = (float) life / maxLife;
        
        // 状态决策逻辑
        if (lifeRatio <= escapeThreshold) {
            // 生命值过低，逃跑
            if (!(this.curState instanceof EscapeState)) {
                switchToEscape();
            }
        } else if (distanceToAvatar <= detectionRange) {
            // 阿凡达在检测范围内，战斗
            if (!(this.curState instanceof FightState)) {
                switchToFight();
            }
        } else {
            // 正常巡逻
            if (!(this.curState instanceof PatrolState)) {
                switchToPatrol();
            }
        }
        
        // 转发给当前状态进行更新
        if (curState != null) curState.update();
    }
    
    // 切换到巡逻状态
    private void switchToPatrol() {
        AnimPara anim = curState != null ? curState.animation.animPara : null;
        if (anim != null) {
            Animation2D anim2d = new Animation2D(anim);
            PatrolState patrol = new PatrolState(this, 100000, 2, anim2d, "");
            this.addState(patrol);
            this.switchState("patrol");
        }
    }
    
    // 切换到战斗状态
    private void switchToFight() {
        AnimPara anim = curState != null ? curState.animation.animPara : null;
        if (anim != null) {
            Animation2D anim2d = new Animation2D(anim);
            FightState fight = new FightState(this, 100000, 3, anim2d, "");
            this.addState(fight);
            this.switchState("fight");
        }
    }
    
    // 切换到逃跑状态
    private void switchToEscape() {
        AnimPara anim = curState != null ? curState.animation.animPara : null;
        if (anim != null) {
            Animation2D anim2d = new Animation2D(anim);
            EscapeState escape = new EscapeState(this, 100000, 5, anim2d, "");
            this.addState(escape);
            this.switchState("escape");
        }
    }
    
    /*
     * 重写状态到期后的回调函数
     * 让怪物负责决定下一个状态
     */
    public void onStateFinish(ActorState state) {
        // 状态结束后重新评估当前情况
        // update方法会自动处理状态切换
    }
}
