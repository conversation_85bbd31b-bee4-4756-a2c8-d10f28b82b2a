package cuc.scenes;

import cuc.GameStarter;
import cuc.GameWorld;
import cuc.scenes.MainMenu;
import javax.swing.ImageIcon;
import java.awt.Graphics;
import java.awt.Image;
import java.awt.event.MouseEvent;

public class Startup extends GameWorld {
    private long startTime;
    private boolean finished = false;
    private Image splashImage;

    public Startup(GameStarter game) {
        super(game);
        // 可替换为你喜欢的开场图片
        splashImage = new ImageIcon(getClass().getClassLoader().getResource("images/xin.png")).getImage();
        startTime = System.currentTimeMillis();
    }

    @Override
    public void render(Graphics g) {
        // 填充背景
        g.setColor(java.awt.Color.WHITE);
        g.fillRect(0, 0, game.getWidth(), game.getHeight());
        // 显示图片
        g.drawImage(splashImage, game.getWidth()/2-150, game.getHeight()/2-150, 300, 300, null);
        // 显示文字
        g.setColor(java.awt.Color.BLACK);
        g.drawString("游戏开场动画...", game.getWidth()/2-50, game.getHeight()/2+180);
        g.drawString("点击鼠标可跳过", game.getWidth()/2-50, game.getHeight()/2+200);
    }

    @Override
    public void update() {
        if (!finished && (System.currentTimeMillis() - startTime > 2000)) {
            finished = true;
            game.switchWorld(new MainMenu(game));
        }
    }

    @Override
    public void mouseReleased(MouseEvent e) {
        if (!finished) {
            finished = true;
            game.switchWorld(new MainMenu(game));
        }
    }
} 