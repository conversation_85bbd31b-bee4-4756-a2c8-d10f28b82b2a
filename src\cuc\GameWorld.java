package cuc;

import java.applet.Applet;
import java.applet.AudioClip;
import java.awt.Graphics;
import java.awt.Image;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.io.*;
import java.util.*;

import javax.swing.ImageIcon;
import cuc.gameObjects.GameObject;
import cuc.gameObjects.Tree;
import cuc.gameObjects.Vender;
import cuc.states.MoveState;
import cuc.states.StopState;
import cuc.gameObjects.Avatar;
import cuc.utils.AmmoPara;
import cuc.utils.AnimPara;
import cuc.utils.Animation2D;
import cuc.utils.Point2D;
import cuc.utils.SoundPara;
import javax.swing.JFileChooser;

//顾名思义，这个类代表着整个游戏世界，其中包含着所有的游戏对象。
public abstract class GameWorld {
	protected GameStarter game;    //反向引用游戏主类对象	
	
	//存储着游戏世界中的所有的游戏对象
	protected ArrayList<GameObject> gameObjects = new ArrayList<GameObject>(); 
	
	//下面两张图是游戏的背景图和前景图，是2.5D游戏特有的技术。
	protected Image bImage;     //游戏场景背景图
	protected Image fImage;     //游戏场景前景图
	
	protected AudioClip bgm;    //游戏背景音乐    
	//所有的动画信息对象
	protected ArrayList<AnimPara> allAnimParas = new ArrayList<AnimPara>();
	//所有的音效信息对象
	protected ArrayList<SoundPara> allSoundParas = new ArrayList<SoundPara>();	
	//所有的弹药参数对象
	protected ArrayList<AmmoPara> allAmmoParas = new ArrayList<AmmoPara>();	
	
	public GameWorld(GameStarter game){
		this.game = game;
		// 移除initGameScene、initAllParas、initAllGameObjects等具体实现，改为抽象或空实现
	}
	//游戏世界的渲染方法
	public void render(Graphics g){
		//先画场景的背景图
    	g.drawImage(bImage,0,0,game.getWidth(),game.getHeight(),null);
    	//通知游戏世界中的游戏对象进行渲染	
    	for(int i = 0; i < gameObjects.size(); i++){
    		gameObjects.get(i).render(g);
    	}    	
    	//后画场景的遮挡图
    	g.drawImage(fImage,0,0,game.getWidth(),game.getHeight(),null);
	}
	//游戏世界的状态更新函数，或者说是游戏仿真函数
	//规定着游戏中的对象经过一帧后的状态变化，比如空间坐标。
	public void update(){
		//通知游戏世界中的游戏对象进行状态更新		
    	for(int i = 0; i < gameObjects.size(); i++){
    		gameObjects.get(i).update();
    	}		
	}
	
	public void keyPressed(int key) {
		//将键盘事件转发给玩家化身对象
		Avatar avatar = getAvatar();
		if (avatar != null) {
			avatar.keyPressed(key);
		}

		//将键盘事件转发给所有Vender对象（用于E键交易）
		for(int i = 0; i < gameObjects.size(); i++){
			if(gameObjects.get(i) instanceof Vender){
				((Vender)gameObjects.get(i)).keyPressed(key);
			}
		}

		// M键保存
		if (key == java.awt.event.KeyEvent.VK_M) {
			JFileChooser chooser = new JFileChooser();
			int ret = chooser.showSaveDialog(null);
			if (ret == JFileChooser.APPROVE_OPTION) {
				String path = chooser.getSelectedFile().getAbsolutePath();
				saveToFile(path);
			}
		}
		// L键读取
		if (key == java.awt.event.KeyEvent.VK_L) {
			JFileChooser chooser = new JFileChooser();
			int ret = chooser.showOpenDialog(null);
			if (ret == JFileChooser.APPROVE_OPTION) {
				String path = chooser.getSelectedFile().getAbsolutePath();
				loadFromFile(path);
			}
		}
	}
	public void mouseReleased(MouseEvent e) {
		getAvatar().mouseReleased(e);

		//将鼠标事件转发给所有Vender对象（用于交易界面点击）
		for(int i = 0; i < gameObjects.size(); i++){
			if(gameObjects.get(i) instanceof Vender){
				((Vender)gameObjects.get(i)).mouseReleased(e);
			}
		}
	}
	public AnimPara getAnimParaByName(String name){
		for(int i = 0; i < allAnimParas.size(); i++){
			if(allAnimParas.get(i).name == name){
				return allAnimParas.get(i);
			}			
		}
		System.out.println("没有找到动画："+name);
		return null;
	}
	public SoundPara getSoundParaByName(String name){
		for(int i = 0; i < allSoundParas.size(); i++){
			if(allSoundParas.get(i).name == name){
				return allSoundParas.get(i);
			}			
		}
		System.out.println("没有找到音效："+name);
		return null;
	}
	public AmmoPara getAmmoParaByName(String name){
		for(int i = 0; i < allAmmoParas.size(); i++){
			if(allAmmoParas.get(i).name == name){
				return allAmmoParas.get(i);
			}			
		}
		System.out.println("没有找到弹药："+name);
		return null;
	}
	//向游戏世界中添加一个游戏对象
	public void addGameObject(GameObject g){
		gameObjects.add(g);
	}
	//删除一个游戏对象
	public void removeGameObject(GameObject g){
		gameObjects.remove(g);
	}
	public ArrayList<GameObject> getAllGameObjects(){
		return gameObjects;
	}
	//找到阿凡达对象
	public Avatar getAvatar(){
		for(int i = 0; i < gameObjects.size(); i++){
    		if(gameObjects.get(i).getClass() == Avatar.class){
    			return (Avatar)gameObjects.get(i);
    		}
    	}
		System.out.println("没有找到阿凡达！");
		return null;
	}	
    // 保存当前场景所有对象到JSON格式文件
    public void saveToFile(String filename) {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filename))) {
            writer.write("[\n");
            for (int i = 0; i < gameObjects.size(); i++) {
                GameObject go = gameObjects.get(i);
                StringBuilder sb = new StringBuilder();
                if (go instanceof cuc.gameObjects.Avatar) {
                    cuc.gameObjects.Avatar a = (cuc.gameObjects.Avatar) go;
                    sb.append("  {\"type\":\"Avatar\",\"x\":" + a.pos.x + ",\"y\":" + a.pos.y + ",\"w\":" + a.size.x + ",\"h\":" + a.size.y + ",\"life\":" + a.life + ",\"direction\":" + a.getCurState().getCurDirection() + ",\"state\":\"" + a.getCurState().name + "\"}");
                } else if (go instanceof cuc.gameObjects.Vender) {
                    cuc.gameObjects.Vender v = (cuc.gameObjects.Vender) go;
                    sb.append("  {\"type\":\"Vender\",\"x\":" + v.pos.x + ",\"y\":" + v.pos.y + ",\"w\":" + v.size.x + ",\"h\":" + v.size.y + ",\"life\":" + v.life + ",\"direction\":" + v.getCurState().getCurDirection() + ",\"state\":\"" + v.getCurState().name + "\"}");
                } else if (go instanceof cuc.gameObjects.Tree) {
                    cuc.gameObjects.Tree t = (cuc.gameObjects.Tree) go;
                    sb.append("  {\"type\":\"Tree\",\"x\":" + t.pos.x + ",\"y\":" + t.pos.y + ",\"w\":" + t.size.x + ",\"h\":" + t.size.y + "}");
                }
                writer.write(sb.toString());
                if (i < gameObjects.size() - 1) writer.write(",");
                writer.write("\n");
            }
            writer.write("]\n");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // 从JSON格式文件加载对象并重建场景
    public void loadFromFile(String filename) {
        try (BufferedReader reader = new BufferedReader(new FileReader(filename))) {
            StringBuilder json = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                json.append(line.trim());
            }
            // 简单解析（假设格式正确）
            String content = json.toString();
            content = content.substring(1, content.length() - 1); // 去掉[]
            String[] items = content.split("},");
            gameObjects.clear();
            for (String item : items) {
                item = item.trim();
                if (!item.endsWith("}")) item = item + "}";
                if (item.length() < 5) continue;
                Map<String, String> map = new HashMap<>();
                item = item.substring(1, item.length() - 1); // 去掉{}
                String[] fields = item.split(",");
                for (String f : fields) {
                    String[] kv = f.split(":");
                    if (kv.length == 2) {
                        String k = kv[0].replaceAll("[\"{}]", "").trim();
                        String v = kv[1].replaceAll("[\"{}]", "").trim();
                        map.put(k, v);
                    }
                }
                String type = map.get("type");
                int x = Integer.parseInt(map.get("x"));
                int y = Integer.parseInt(map.get("y"));
                int w = Integer.parseInt(map.get("w"));
                int h = Integer.parseInt(map.get("h"));
                if ("Avatar".equals(type)) {
                    int life = Integer.parseInt(map.get("life"));
                    int direction = Integer.parseInt(map.get("direction"));
                    String state = map.get("state");
                    cuc.gameObjects.Avatar avatar = new cuc.gameObjects.Avatar(this, new cuc.utils.Point2D(x, y), new cuc.utils.Point2D(w, h), life, getAmmoParaByName("飞蛇箭"));
                    // 添加所有状态对象
                    AnimPara stopAI = getAnimParaByName("土匪站立");
                    Animation2D stopAnim = new Animation2D(stopAI);
                    StopState stop = new StopState(avatar, 1000000000, 0, stopAnim, "");
                    avatar.addState(stop);
                    AnimPara moveAI = getAnimParaByName("土匪行走");
                    Animation2D moveAnim = new Animation2D(moveAI);
                    MoveState move = new MoveState(avatar, 2000, 8, moveAnim, "走路声.wav");
                    avatar.addState(move);
                    // 设置当前状态和方向
                    avatar.setCurState(avatar.getStateByName(state));
                    if (avatar.getCurState() != null) {
                        avatar.getCurState().setCurDirection(direction);
                    }
                    this.addGameObject(avatar);
                } else if ("Vender".equals(type)) {
                    int life = Integer.parseInt(map.get("life"));
                    int direction = Integer.parseInt(map.get("direction"));
                    String state = map.get("state");
                    cuc.gameObjects.Vender vender = new cuc.gameObjects.Vender(this, new cuc.utils.Point2D(x, y), new cuc.utils.Point2D(w, h), life, getAmmoParaByName("火弹"));
                    // 添加所有状态对象
                    AnimPara stopAI1 = getAnimParaByName("货郎站立");
                    Animation2D stopAnim1 = new Animation2D(stopAI1);
                    StopState stop1 = new StopState(vender, 3000, 0, stopAnim1, "");
                    vender.addState(stop1);
                    AnimPara moveAI1 = getAnimParaByName("货郎行走");
                    Animation2D moveAnim1 = new Animation2D(moveAI1);
                    MoveState move1 = new MoveState(vender, 2000, 4, moveAnim1, "走路声.wav");
                    vender.addState(move1);
                    // 设置当前状态和方向
                    vender.setCurState(vender.getStateByName(state));
                    if (vender.getCurState() != null) {
                        vender.getCurState().setCurDirection(direction);
                    }
                    this.addGameObject(vender);
                } else if ("Tree".equals(type)) {
                    cuc.gameObjects.Tree tree = new cuc.gameObjects.Tree(this, new cuc.utils.Point2D(x, y), new cuc.utils.Point2D(w, h), "tree6.png");
                    this.addGameObject(tree);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
