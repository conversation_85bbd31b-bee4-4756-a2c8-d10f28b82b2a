package cuc.gameObjects;

import java.util.ArrayList;
import cuc.GameWorld;
import cuc.states.ActorState;
import cuc.states.PatrolState;
import cuc.states.FightState;
import cuc.states.EscapeState;
import cuc.utils.AmmoPara;
import cuc.utils.Point2D;
import cuc.utils.AnimPara;
import cuc.utils.Animation2D;

/*
 * 创建一个Monster的子类：蜘蛛类。
 * 这是一个特殊的怪物，具有以下特点：
 * 1. 使用蜘蛛.jpg图片
 * 2. 移动速度较快
 * 3. 攻击频率较高
 * 4. 具有更敏锐的检测能力
 * 5. 巡逻范围较小但更灵活
 */
public class Spider extends Monster {
    
    public Spider(GameWorld game, Point2D pos, Point2D size, int life, AmmoPara ammoPara) {
        super(game, pos, size, life, ammoPara);
        
        // 蜘蛛的特殊属性设置
        detectionRange = 250;             // 更敏锐的检测距离
        escapeRange = 350;                // 更远的逃跑距离
        escapeThreshold = 0.2f;           // 生命值低于20%时逃跑（更早逃跑）
        
        // 设置蜘蛛的默认巡逻路径（小范围巡逻）
        patrolPoints = new ArrayList<Point2D>();
        patrolPoints.add(new Point2D(pos.x - 80, pos.y - 80));
        patrolPoints.add(new Point2D(pos.x + 80, pos.y - 80));
        patrolPoints.add(new Point2D(pos.x + 80, pos.y + 80));
        patrolPoints.add(new Point2D(pos.x - 80, pos.y + 80));
        currentPatrolIndex = 0;
        patrolForward = true;
    }
    
    // 重写状态更新函数，添加蜘蛛独特的行为逻辑
    @Override
    public void update() {
        // 检查阿凡达是否存在
        if (avatar == null) {
            avatar = gameWorld.getAvatar();
            if (avatar == null) {
                // 如果没有阿凡达，继续巡逻
                if (!(this.curState instanceof PatrolState)) {
                    switchToPatrol();
                }
                if (curState != null) curState.update();
                return;
            }
        }
        
        double distanceToAvatar = Point2D.distance(this.pos, avatar.pos);
        float lifeRatio = (float) life / maxLife;
        
        // 蜘蛛的特殊行为：更激进的战斗策略
        if (lifeRatio <= escapeThreshold) {
            // 生命值过低，逃跑
            if (!(this.curState instanceof EscapeState)) {
                switchToEscape();
            }
        } else if (distanceToAvatar <= detectionRange) {
            // 阿凡达在检测范围内，战斗
            if (!(this.curState instanceof FightState)) {
                switchToFight();
            }
        } else {
            // 正常巡逻
            if (!(this.curState instanceof PatrolState)) {
                switchToPatrol();
            }
        }
        
        // 转发给当前状态进行更新
        if (curState != null) curState.update();
    }
    
    // 蜘蛛的切换到巡逻状态 - 蜘蛛巡逻速度更快
    private void switchToPatrol() {
        ActorState existingPatrol = getStateByName("patrol");
        if (existingPatrol == null) {
            AnimPara anim = curState != null ? curState.animation.animPara : null;
            if (anim != null) {
                Animation2D anim2d = new Animation2D(anim);
                PatrolState patrol = new PatrolState(this, 100000, 3, anim2d, ""); // 速度3，比普通怪物快
                this.addState(patrol);
            }
        }
        this.switchState("patrol");
    }
    
    // 蜘蛛的切换到战斗状态 - 蜘蛛战斗更激进
    private void switchToFight() {
        ActorState existingFight = getStateByName("fight");
        if (existingFight == null) {
            AnimPara anim = curState != null ? curState.animation.animPara : null;
            if (anim != null) {
                Animation2D anim2d = new Animation2D(anim);
                FightState fight = new FightState(this, 100000, 4, anim2d, ""); // 速度4，更快的战斗移动
                this.addState(fight);
            }
        }
        this.switchState("fight");
    }
    
    // 蜘蛛的切换到逃跑状态 - 蜘蛛逃跑速度很快
    private void switchToEscape() {
        ActorState existingEscape = getStateByName("escape");
        if (existingEscape == null) {
            AnimPara anim = curState != null ? curState.animation.animPara : null;
            if (anim != null) {
                Animation2D anim2d = new Animation2D(anim);
                EscapeState escape = new EscapeState(this, 100000, 6, anim2d, ""); // 速度6，非常快的逃跑
                this.addState(escape);
            }
        }
        this.switchState("escape");
    }
    
    /*
     * 重写状态到期后的回调函数
     * 蜘蛛的状态切换更频繁
     */
    @Override
    public void onStateFinish(ActorState state) {
        // 状态结束后重新评估当前情况
        // update方法会自动处理状态切换
    }
}
