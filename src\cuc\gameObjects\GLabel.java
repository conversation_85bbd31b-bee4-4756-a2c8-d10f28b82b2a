package cuc.gameObjects;

import java.awt.*;
import cuc.GameWorld;
import cuc.utils.Point2D;

/*
 * 自定义标签类：用于显示美观的文字
 * 继承GameObject，可以像游戏对象一样统一管理
 */
public class GLabel extends GameObject {
    private String text;           // 显示的文字
    private Font font;             // 字体
    private Color textColor;       // 文字颜色
    private Color backgroundColor; // 背景颜色
    private Color borderColor;     // 边框颜色
    private boolean hasBorder;     // 是否有边框
    private boolean hasBackground; // 是否有背景
    private int padding;           // 内边距
    private int borderWidth;       // 边框宽度
    private boolean visible;       // 是否可见
    private int textAlign;         // 文字对齐方式：0=左对齐，1=居中，2=右对齐
    
    // 文字对齐常量
    public static final int ALIGN_LEFT = 0;
    public static final int ALIGN_CENTER = 1;
    public static final int ALIGN_RIGHT = 2;
    
    public GLabel(GameWorld game, Point2D pos, Point2D size, String text) {
        this.gameWorld = game;
        this.pos = new Point2D(pos.x, pos.y);
        this.size = new Point2D(size.x, size.y);
        this.text = text;
        
        // 默认样式设置
        this.font = new Font("微软雅黑", Font.PLAIN, 16);
        this.textColor = Color.BLACK;
        this.backgroundColor = new Color(255, 255, 255, 200); // 半透明白色
        this.borderColor = new Color(100, 100, 100);
        this.hasBorder = true;
        this.hasBackground = true;
        this.padding = 8;
        this.borderWidth = 2;
        this.visible = true;
        this.textAlign = ALIGN_CENTER;
        
        // 不需要碰撞盒
        this.collider = null;
    }
    
    @Override
    public void render(Graphics g) {
        if (!visible) return;
        
        Graphics2D g2d = (Graphics2D) g;
        // 开启抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        
        int x = pos.x;
        int y = pos.y;
        int width = size.x;
        int height = size.y;
        
        // 绘制背景
        if (hasBackground) {
            g2d.setColor(backgroundColor);
            g2d.fillRoundRect(x, y, width, height, 10, 10); // 圆角矩形
        }
        
        // 绘制边框
        if (hasBorder) {
            g2d.setColor(borderColor);
            g2d.setStroke(new BasicStroke(borderWidth));
            g2d.drawRoundRect(x, y, width, height, 10, 10);
        }
        
        // 绘制文字
        if (text != null && !text.isEmpty()) {
            g2d.setFont(font);
            g2d.setColor(textColor);
            
            FontMetrics fm = g2d.getFontMetrics();
            int textWidth = fm.stringWidth(text);
            int textHeight = fm.getHeight();
            
            // 计算文字位置
            int textX, textY;
            
            // 垂直居中
            textY = y + (height - textHeight) / 2 + fm.getAscent();
            
            // 水平对齐
            switch (textAlign) {
                case ALIGN_LEFT:
                    textX = x + padding;
                    break;
                case ALIGN_RIGHT:
                    textX = x + width - textWidth - padding;
                    break;
                case ALIGN_CENTER:
                default:
                    textX = x + (width - textWidth) / 2;
                    break;
            }
            
            g2d.drawString(text, textX, textY);
        }
    }
    
    // Getter和Setter方法
    public String getText() {
        return text;
    }
    
    public void setText(String text) {
        this.text = text;
    }
    
    public Font getFont() {
        return font;
    }
    
    public void setFont(Font font) {
        this.font = font;
    }
    
    public Color getTextColor() {
        return textColor;
    }
    
    public void setTextColor(Color textColor) {
        this.textColor = textColor;
    }
    
    public Color getBackgroundColor() {
        return backgroundColor;
    }
    
    public void setBackgroundColor(Color backgroundColor) {
        this.backgroundColor = backgroundColor;
    }
    
    public Color getBorderColor() {
        return borderColor;
    }
    
    public void setBorderColor(Color borderColor) {
        this.borderColor = borderColor;
    }
    
    public boolean isHasBorder() {
        return hasBorder;
    }
    
    public void setHasBorder(boolean hasBorder) {
        this.hasBorder = hasBorder;
    }
    
    public boolean isHasBackground() {
        return hasBackground;
    }
    
    public void setHasBackground(boolean hasBackground) {
        this.hasBackground = hasBackground;
    }
    
    public int getPadding() {
        return padding;
    }
    
    public void setPadding(int padding) {
        this.padding = padding;
    }
    
    public int getBorderWidth() {
        return borderWidth;
    }
    
    public void setBorderWidth(int borderWidth) {
        this.borderWidth = borderWidth;
    }
    
    public boolean isVisible() {
        return visible;
    }
    
    public void setVisible(boolean visible) {
        this.visible = visible;
    }
    
    public int getTextAlign() {
        return textAlign;
    }
    
    public void setTextAlign(int textAlign) {
        this.textAlign = textAlign;
    }
    
    // 便捷的样式设置方法
    public void setStyle(Color textColor, Color backgroundColor, Color borderColor) {
        this.textColor = textColor;
        this.backgroundColor = backgroundColor;
        this.borderColor = borderColor;
    }
    
    public void setTransparent() {
        this.hasBackground = false;
        this.hasBorder = false;
    }
    
    public void setSimpleStyle() {
        this.hasBackground = true;
        this.hasBorder = true;
        this.backgroundColor = new Color(240, 240, 240, 220);
        this.borderColor = new Color(120, 120, 120);
        this.textColor = Color.BLACK;
    }
    
    public void setHighlightStyle() {
        this.hasBackground = true;
        this.hasBorder = true;
        this.backgroundColor = new Color(255, 255, 0, 180); // 半透明黄色
        this.borderColor = new Color(255, 165, 0);
        this.textColor = Color.BLACK;
    }
}
