package cuc.scenes;

import cuc.GameStarter;
import cuc.GameWorld;
import cuc.gameObjects.GButton;
import cuc.gameObjects.GLabel;
import cuc.utils.Point2D;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.event.MouseEvent;
import javax.swing.JFileChooser;
import java.io.File;

public class MainMenu extends GameWorld {
    private GButton startButton;
    private GButton loadButton;
    private GButton exitButton;
    private GLabel titleLabel;

    public MainMenu(GameStarter game) {
        super(game);
        createMenuInterface();
    }

    private void createMenuInterface() {
        int centerX = game.getWidth() / 2;
        int btnW = 250, btnH = 60;

        // 创建标题标签
        titleLabel = new GLabel(this, new Point2D(centerX - 200, 150), new Point2D(400, 80), "游戏主菜单");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 48));
        titleLabel.setTextColor(new Color(70, 130, 180));
        titleLabel.setTransparent();
        this.addGameObject(titleLabel);

        // 创建开始游戏按钮
        startButton = new GButton(this, new Point2D(centerX - btnW/2, 280), new Point2D(btnW, btnH), "开始游戏");
        startButton.setGreenStyle();
        startButton.setFont(new Font("微软雅黑", Font.BOLD, 24));
        startButton.setClickListener(new GButton.ButtonClickListener() {
            @Override
            public void onClick(GButton button) {
                game.switchWorld(new TaohuaIslandWorld(game));
            }
        });
        this.addGameObject(startButton);

        // 创建读取存档按钮
        loadButton = new GButton(this, new Point2D(centerX - btnW/2, 360), new Point2D(btnW, btnH), "读取存档");
        loadButton.setOrangeStyle();
        loadButton.setFont(new Font("微软雅黑", Font.BOLD, 24));
        loadButton.setClickListener(new GButton.ButtonClickListener() {
            @Override
            public void onClick(GButton button) {
                loadGame();
            }
        });
        this.addGameObject(loadButton);

        // 创建退出游戏按钮
        exitButton = new GButton(this, new Point2D(centerX - btnW/2, 440), new Point2D(btnW, btnH), "退出游戏");
        exitButton.setRedStyle();
        exitButton.setFont(new Font("微软雅黑", Font.BOLD, 24));
        exitButton.setClickListener(new GButton.ButtonClickListener() {
            @Override
            public void onClick(GButton button) {
                System.exit(0);
            }
        });
        this.addGameObject(exitButton);
    }

    private void loadGame() {
        JFileChooser chooser = new JFileChooser();
        int ret = chooser.showOpenDialog(null);
        if (ret == JFileChooser.APPROVE_OPTION) {
            File file = chooser.getSelectedFile();
            TaohuaIslandWorld world = new TaohuaIslandWorld(game);
            world.loadFromFile(file.getAbsolutePath());
            game.switchWorld(world);
        }
    }

    @Override
    public void render(Graphics g) {
        Graphics2D g2d = (Graphics2D) g;
        // 开启抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 绘制渐变背景
        java.awt.GradientPaint gradient = new java.awt.GradientPaint(
            0, 0, new Color(135, 206, 250),  // 天蓝色
            0, game.getHeight(), new Color(70, 130, 180)  // 钢蓝色
        );
        g2d.setPaint(gradient);
        g2d.fillRect(0, 0, game.getWidth(), game.getHeight());

        // 调用父类的render方法来渲染所有游戏对象（包括按钮）
        super.render(g);
    }

    @Override
    public void mouseReleased(MouseEvent e) {
        // 转发鼠标事件给按钮
        startButton.mouseReleased(e);
        loadButton.mouseReleased(e);
        exitButton.mouseReleased(e);
    }

    @Override
    public void mouseMoved(MouseEvent e) {
        // 转发鼠标移动事件给按钮
        startButton.mouseMoved(e);
        loadButton.mouseMoved(e);
        exitButton.mouseMoved(e);
    }
}