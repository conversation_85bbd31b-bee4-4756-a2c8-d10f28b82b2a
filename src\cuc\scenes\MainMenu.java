package cuc.scenes;

import cuc.GameStarter;
import cuc.GameWorld;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.event.MouseEvent;
import javax.swing.JFileChooser;
import java.io.File;

public class MainMenu extends GameWorld {
    // 按钮区域
    private final int startX = 500, startY = 300, btnW = 200, btnH = 50;
    private final int loadX = 500, loadY = 350;
    private final int exitX = 500, exitY = 400;

    public MainMenu(GameStarter game) {
        super(game);
    }

    @Override
    public void render(Graphics g) {
        // 背景
        g.setColor(Color.LIGHT_GRAY);
        g.fillRect(0, 0, game.getWidth(), game.getHeight());
        // 标题
        g.setColor(Color.BLACK);
        g.setFont(new Font("微软雅黑", Font.BOLD, 48));
        g.drawString("游戏主菜单", game.getWidth()/2-150, 200);
        // 开始游戏按钮
        g.setColor(Color.GREEN);
        g.fillRect(startX, startY, btnW, btnH);
        g.setColor(Color.BLACK);
        g.setFont(new Font("微软雅黑", Font.PLAIN, 28));
        g.drawString("开始游戏", startX+40, startY+35);
        // 读取存档按钮
        g.setColor(Color.ORANGE);
        g.fillRect(loadX, loadY, btnW, btnH);
        g.setColor(Color.BLACK);
        g.drawString("读取存档", loadX+40, loadY+35);
        // 退出游戏按钮
        g.setColor(Color.RED);
        g.fillRect(exitX, exitY, btnW, btnH);
        g.setColor(Color.BLACK);
        g.drawString("退出游戏", exitX+40, exitY+35);
    }

    @Override
    public void mouseReleased(MouseEvent e) {
        int mx = e.getX(), my = e.getY();
        if (mx >= startX && mx <= startX+btnW && my >= startY && my <= startY+btnH) {
            // 开始游戏
            game.switchWorld(new TaohuaIslandWorld(game));
        } else if (mx >= loadX && mx <= loadX+btnW && my >= loadY && my <= loadY+btnH) {
            // 读取存档
            JFileChooser chooser = new JFileChooser();
            int ret = chooser.showOpenDialog(null);
            if (ret == JFileChooser.APPROVE_OPTION) {
                File file = chooser.getSelectedFile();
                TaohuaIslandWorld world = new TaohuaIslandWorld(game);
                world.loadFromFile(file.getAbsolutePath());
                game.switchWorld(world);
            }
        } else if (mx >= exitX && mx <= exitX+btnW && my >= exitY && my <= exitY+btnH) {
            // 退出游戏
            System.exit(0);
        }
    }
} 