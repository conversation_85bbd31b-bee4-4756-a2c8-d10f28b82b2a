package cuc.scenes;

import cuc.GameStarter;
import cuc.GameWorld;
import cuc.gameObjects.*;
import cuc.states.*;
import cuc.utils.*;
import cuc.scenes.AnotherWorld;
import javax.swing.ImageIcon;
import java.awt.Image;
import java.util.ArrayList;

public class TaohuaIslandWorld extends GameWorld {
    public TaohuaIslandWorld(GameStarter game) {
        super(game);
        initGameScene();
    }
    void initGameScene() {
        bImage = new ImageIcon(getClass().getClassLoader().getResource("images/桃花岛-1.jpg")).getImage();
        fImage = new ImageIcon(getClass().getClassLoader().getResource("images/桃花岛-1-2.png")).getImage();
        initAllParas();
        initAllGameObjects();
    }
    void initAllParas() {
        allAnimParas.add(new AnimPara("土匪站立","土匪站立.png",1,8,new int[]{4,6,2,0,5,3,7,1}));
        allAnimParas.add(new AnimPara("土匪行走","土匪行走.png",8,8,new int[]{4,6,2,0,5,3,7,1}));
        allAnimParas.add(new AnimPara("土匪跑步","土匪行走.png",8,8,new int[]{4,6,2,0,5,3,7,1}));
        allAnimParas.add(new AnimPara("货郎站立","货郎站立.png",1,4,new int[]{0,1,2,3}));
        allAnimParas.add(new AnimPara("货郎行走","货郎行走.png",4,4,new int[]{0,1,2,3}));
        allAnimParas.add(new AnimPara("货郎跑步","货郎行走.png",4,4,new int[]{0,1,2,3}));
        allAnimParas.add(new AnimPara("书生站立","书生站立.png",1,4,new int[]{0,1,2,3}));
        allAnimParas.add(new AnimPara("书生行走","书生行走.png",4,4,new int[]{0,1,2,3}));
        allAnimParas.add(new AnimPara("书生跑步","书生行走.png",4,4,new int[]{0,1,2,3}));
        // 怪物动画参数（使用书生的图片作为怪物）
        allAnimParas.add(new AnimPara("怪物站立","书生站立.png",1,4,new int[]{0,1,2,3}));
        allAnimParas.add(new AnimPara("怪物行走","书生行走.png",4,4,new int[]{0,1,2,3}));
        allAnimParas.add(new AnimPara("飞蛇箭","飞蛇箭.png",8,8,new int[]{0,2,6,4,1,7,3,5}));
        allAnimParas.add(new AnimPara("火弹","火弹.png",8,1,new int[]{0}));
        allSoundParas.add(new SoundPara("弓箭发射声","弓箭发射.wav"));
        allSoundParas.add(new SoundPara("走路声","走路声.wav"));
        allAmmoParas.add(new AmmoPara("飞蛇箭",new Point2D(50,50),20,3000,30,getAnimParaByName("飞蛇箭"),getSoundParaByName("弓箭发射声"),null));
        allAmmoParas.add(new AmmoPara("火弹",new Point2D(50,50),20,3000,30,getAnimParaByName("火弹"),getSoundParaByName("弓箭发射声"),null));
        // 怪物弹药（使用火弹）
        allAmmoParas.add(new AmmoPara("怪物弹药",new Point2D(30,30),15,2000,25,getAnimParaByName("火弹"),getSoundParaByName("弓箭发射声"),null));
    }
    void initAllGameObjects() {
        Avatar avatar = new Avatar(this,new Point2D(400,400),new Point2D(128,152),100,getAmmoParaByName("飞蛇箭"));
        AnimPara stopAI = this.getAnimParaByName("土匪站立");
        Animation2D stopAnim = new Animation2D(stopAI);
        StopState stop = new StopState(avatar,1000000000,0,stopAnim,"");
        avatar.addState(stop);
        AnimPara moveAI = this.getAnimParaByName("土匪行走");
        Animation2D moveAnim = new Animation2D(moveAI);
        MoveState move = new MoveState(avatar,2000,8,moveAnim,"走路声.wav");
        avatar.addState(move);
        avatar.setCurState(stop);
        stop.start();
        this.addGameObject(avatar);

        Vender vender = new Vender(this,new Point2D(400,300),new Point2D(64,76),100,getAmmoParaByName("火弹"));
        AnimPara stopAI1 = this.getAnimParaByName("货郎站立");
        Animation2D stopAnim1 = new Animation2D(stopAI1);
        StopState stop1 = new StopState(vender,3000,0,stopAnim1,"");
        vender.addState(stop1);
        AnimPara moveAI1 = this.getAnimParaByName("货郎行走");
        Animation2D moveAnim1 = new Animation2D(moveAI1);
        MoveState move1 = new MoveState(vender,2000,4,moveAnim1,"走路声.wav");
        vender.addState(move1);
        vender.setCurState(stop1);
        stop1.start();
        this.addGameObject(vender);

        // 创建怪物
        Monster monster = new Monster(this,new Point2D(600,300),new Point2D(64,76),80,getAmmoParaByName("怪物弹药"));
        // 设置怪物的巡逻路径
        ArrayList<Point2D> patrolPath = new ArrayList<Point2D>();
        patrolPath.add(new Point2D(500, 300));
        patrolPath.add(new Point2D(700, 300));
        patrolPath.add(new Point2D(700, 500));
        patrolPath.add(new Point2D(500, 500));
        monster.setPatrolPath(patrolPath);

        // 添加怪物的巡逻状态
        AnimPara monsterStopAI = this.getAnimParaByName("怪物站立");
        Animation2D monsterStopAnim = new Animation2D(monsterStopAI);
        PatrolState patrol = new PatrolState(monster, 100000, 2, monsterStopAnim, "");
        monster.addState(patrol);

        // 添加怪物的战斗状态
        AnimPara monsterMoveAI = this.getAnimParaByName("怪物行走");
        Animation2D monsterMoveAnim = new Animation2D(monsterMoveAI);
        FightState fight = new FightState(monster, 100000, 3, monsterMoveAnim, "");
        monster.addState(fight);

        // 添加怪物的逃跑状态
        Animation2D monsterEscapeAnim = new Animation2D(monsterMoveAI);
        EscapeState escape = new EscapeState(monster, 100000, 5, monsterEscapeAnim, "");
        monster.addState(escape);

        monster.setCurState(patrol);
        patrol.start();
        this.addGameObject(monster);

        this.addGameObject(new Tree(this,new Point2D(700,300),new Point2D(200,300),"tree6.png"));
    }
    @Override
    public void update() {
        super.update();
        Avatar avatar = getAvatar();
        if (avatar != null && avatar.getPos().x > 1000 && avatar.getPos().y > 600) {
            game.switchWorld(new AnotherWorld(game));
        }
    }
} 