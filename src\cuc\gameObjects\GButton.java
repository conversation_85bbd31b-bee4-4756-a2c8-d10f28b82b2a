package cuc.gameObjects;

import java.awt.*;
import java.awt.event.MouseEvent;
import cuc.GameWorld;
import cuc.utils.Point2D;

/*
 * 自定义按钮类：用于创建美观的可点击按钮
 * 继承GameObject，可以像游戏对象一样统一管理
 */
public class GButton extends GameObject {
    private String text;              // 按钮文字
    private Font font;                // 字体
    private Color textColor;          // 文字颜色
    private Color backgroundColor;    // 背景颜色
    private Color borderColor;        // 边框颜色
    private Color hoverColor;         // 鼠标悬停颜色
    private Color pressedColor;       // 按下时颜色
    private boolean visible;          // 是否可见
    private boolean enabled;          // 是否启用
    private boolean isHovered;        // 是否鼠标悬停
    private boolean isPressed;        // 是否被按下
    private int borderWidth;          // 边框宽度
    private int cornerRadius;         // 圆角半径
    private ButtonClickListener clickListener; // 点击事件监听器
    
    // 按钮点击事件接口
    public interface ButtonClickListener {
        void onClick(GButton button);
    }
    
    public GButton(GameWorld game, Point2D pos, Point2D size, String text) {
        this.gameWorld = game;
        this.pos = new Point2D(pos.x, pos.y);
        this.size = new Point2D(size.x, size.y);
        this.text = text;
        
        // 默认样式设置
        this.font = new Font("微软雅黑", Font.BOLD, 18);
        this.textColor = Color.WHITE;
        this.backgroundColor = new Color(70, 130, 180);      // 钢蓝色
        this.borderColor = new Color(50, 100, 150);
        this.hoverColor = new Color(100, 150, 200);          // 悬停时的浅蓝色
        this.pressedColor = new Color(40, 90, 140);          // 按下时的深蓝色
        this.visible = true;
        this.enabled = true;
        this.isHovered = false;
        this.isPressed = false;
        this.borderWidth = 2;
        this.cornerRadius = 15;
        
        // 不需要碰撞盒
        this.collider = null;
    }
    
    @Override
    public void render(Graphics g) {
        if (!visible) return;
        
        Graphics2D g2d = (Graphics2D) g;
        // 开启抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        
        int x = pos.x;
        int y = pos.y;
        int width = size.x;
        int height = size.y;
        
        // 根据状态选择颜色
        Color currentBgColor = backgroundColor;
        if (!enabled) {
            currentBgColor = new Color(150, 150, 150); // 禁用时的灰色
        } else if (isPressed) {
            currentBgColor = pressedColor;
        } else if (isHovered) {
            currentBgColor = hoverColor;
        }
        
        // 绘制按钮阴影（3D效果）
        if (enabled && !isPressed) {
            g2d.setColor(new Color(0, 0, 0, 50));
            g2d.fillRoundRect(x + 3, y + 3, width, height, cornerRadius, cornerRadius);
        }
        
        // 绘制按钮背景
        g2d.setColor(currentBgColor);
        g2d.fillRoundRect(x, y, width, height, cornerRadius, cornerRadius);
        
        // 绘制渐变效果
        if (enabled) {
            GradientPaint gradient = new GradientPaint(
                x, y, new Color(255, 255, 255, 50),
                x, y + height / 3, new Color(255, 255, 255, 0)
            );
            g2d.setPaint(gradient);
            g2d.fillRoundRect(x, y, width, height / 3, cornerRadius, cornerRadius);
        }
        
        // 绘制边框
        g2d.setColor(borderColor);
        g2d.setStroke(new BasicStroke(borderWidth));
        g2d.drawRoundRect(x, y, width, height, cornerRadius, cornerRadius);
        
        // 绘制文字
        if (text != null && !text.isEmpty()) {
            g2d.setFont(font);
            g2d.setColor(enabled ? textColor : Color.GRAY);
            
            FontMetrics fm = g2d.getFontMetrics();
            int textWidth = fm.stringWidth(text);
            int textHeight = fm.getHeight();
            
            // 文字居中
            int textX = x + (width - textWidth) / 2;
            int textY = y + (height - textHeight) / 2 + fm.getAscent();
            
            // 按下时文字稍微偏移
            if (isPressed && enabled) {
                textX += 1;
                textY += 1;
            }
            
            g2d.drawString(text, textX, textY);
        }
    }
    
    // 处理鼠标事件
    public void mousePressed(MouseEvent e) {
        if (!enabled || !visible) return;
        
        if (isPointInside(e.getX(), e.getY())) {
            isPressed = true;
        }
    }
    
    public void mouseReleased(MouseEvent e) {
        if (!enabled || !visible) return;
        
        if (isPressed && isPointInside(e.getX(), e.getY())) {
            // 触发点击事件
            if (clickListener != null) {
                clickListener.onClick(this);
            }
        }
        isPressed = false;
    }
    
    public void mouseMoved(MouseEvent e) {
        if (!enabled || !visible) return;
        
        isHovered = isPointInside(e.getX(), e.getY());
    }
    
    // 检查点是否在按钮内
    private boolean isPointInside(int x, int y) {
        return x >= pos.x && x <= pos.x + size.x && 
               y >= pos.y && y <= pos.y + size.y;
    }
    
    // Getter和Setter方法
    public String getText() {
        return text;
    }
    
    public void setText(String text) {
        this.text = text;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        if (!enabled) {
            isHovered = false;
            isPressed = false;
        }
    }
    
    public boolean isVisible() {
        return visible;
    }
    
    public void setVisible(boolean visible) {
        this.visible = visible;
    }
    
    public void setClickListener(ButtonClickListener listener) {
        this.clickListener = listener;
    }
    
    public void setColors(Color backgroundColor, Color hoverColor, Color pressedColor) {
        this.backgroundColor = backgroundColor;
        this.hoverColor = hoverColor;
        this.pressedColor = pressedColor;
    }
    
    public void setTextColor(Color textColor) {
        this.textColor = textColor;
    }
    
    public void setFont(Font font) {
        this.font = font;
    }
    
    // 预设样式
    public void setGreenStyle() {
        setColors(new Color(60, 179, 113), new Color(90, 200, 140), new Color(40, 150, 90));
        this.borderColor = new Color(40, 120, 80);
    }
    
    public void setRedStyle() {
        setColors(new Color(220, 20, 60), new Color(240, 50, 80), new Color(180, 10, 40));
        this.borderColor = new Color(150, 10, 30);
    }
    
    public void setOrangeStyle() {
        setColors(new Color(255, 140, 0), new Color(255, 165, 30), new Color(220, 110, 0));
        this.borderColor = new Color(200, 100, 0);
    }
    
    public void setGrayStyle() {
        setColors(new Color(128, 128, 128), new Color(160, 160, 160), new Color(100, 100, 100));
        this.borderColor = new Color(80, 80, 80);
    }
}
