package cuc.states;

import cuc.gameObjects.Actor;
import cuc.gameObjects.GButton;
import cuc.gameObjects.GDialog;
import cuc.gameObjects.GLabel;
import cuc.utils.Animation2D;
import cuc.utils.Point2D;
import java.awt.Graphics;
import java.awt.event.MouseEvent;
import java.util.ArrayList;

public class TradeState extends ActorState {
    private String[] goods = {"药水", "剑", "盾"};
    private int[] prices = {10, 50, 30};
    private GDialog tradeDialog;
    private ArrayList<GButton> goodsButtons;
    private GLabel statusLabel;

    public TradeState(Actor host, float maxLifeTime, int speed, Animation2D animation) {
        super(host, maxLifeTime, speed, animation, "");
        this.name = "trade";

        // 创建交易界面
        createTradeInterface();
    }

    private void createTradeInterface() {
        // 创建主对话框
        int dialogWidth = 350;
        int dialogHeight = 250;
        int x = host.getPos().x - dialogWidth/2;
        int y = host.getPos().y - host.getSize().y/2 - dialogHeight - 30;

        tradeDialog = new GDialog(host.gameWorld, new Point2D(x, y), new Point2D(dialogWidth, dialogHeight),
                                 "货郎商店", "欢迎光临！请选择您要购买的商品：");
        tradeDialog.setSuccessStyle();
        host.gameWorld.addGameObject(tradeDialog);

        // 创建商品按钮
        goodsButtons = new ArrayList<>();
        for (int i = 0; i < goods.length; i++) {
            int buttonWidth = 280;
            int buttonHeight = 35;
            int buttonX = x + 35;
            int buttonY = y + 80 + i * 45;

            String buttonText = goods[i] + " - " + prices[i] + "金币";
            GButton button = new GButton(host.gameWorld, new Point2D(buttonX, buttonY),
                                       new Point2D(buttonWidth, buttonHeight), buttonText);

            // 设置按钮样式
            if (i == 0) button.setGreenStyle();      // 药水 - 绿色
            else if (i == 1) button.setOrangeStyle(); // 剑 - 橙色
            else button.setGrayStyle();               // 盾 - 灰色

            final int index = i;
            button.setClickListener(new GButton.ButtonClickListener() {
                @Override
                public void onClick(GButton btn) {
                    purchaseItem(index);
                }
            });

            goodsButtons.add(button);
            host.gameWorld.addGameObject(button);
        }

        // 创建状态标签
        statusLabel = new GLabel(host.gameWorld, new Point2D(x + 35, y + 210),
                               new Point2D(280, 30), "");
        statusLabel.setTransparent();
        statusLabel.setTextColor(java.awt.Color.BLUE);
        statusLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        host.gameWorld.addGameObject(statusLabel);
    }

    private void purchaseItem(int index) {
        statusLabel.setText("已购买：" + goods[index] + "！感谢惠顾！");
        statusLabel.setHighlightStyle();

        // 禁用已购买的按钮
        goodsButtons.get(index).setEnabled(false);
        goodsButtons.get(index).setText(goods[index] + " - 已售出");
    }

    @Override
    public void render(Graphics g) {
        super.render(g);

        // 更新界面位置，跟随角色
        updateInterfacePosition();
    }

    private void updateInterfacePosition() {
        if (tradeDialog == null) return;

        int dialogWidth = 350;
        int dialogHeight = 250;
        int x = host.getPos().x - dialogWidth/2;
        int y = host.getPos().y - host.getSize().y/2 - dialogHeight - 30;

        // 更新对话框位置
        tradeDialog.setPos(new Point2D(x, y));

        // 更新按钮位置
        for (int i = 0; i < goodsButtons.size(); i++) {
            int buttonX = x + 35;
            int buttonY = y + 80 + i * 45;
            goodsButtons.get(i).setPos(new Point2D(buttonX, buttonY));
        }

        // 更新状态标签位置
        statusLabel.setPos(new Point2D(x + 35, y + 210));
    }

    public void mouseReleased(MouseEvent e) {
        // 转发鼠标事件给按钮
        for (GButton button : goodsButtons) {
            button.mouseReleased(e);
        }
    }

    @Override
    public void stop() {
        super.stop();
        // 清理界面元素
        if (tradeDialog != null) {
            host.gameWorld.removeGameObject(tradeDialog);
            tradeDialog = null;
        }

        if (goodsButtons != null) {
            for (GButton button : goodsButtons) {
                host.gameWorld.removeGameObject(button);
            }
            goodsButtons.clear();
        }

        if (statusLabel != null) {
            host.gameWorld.removeGameObject(statusLabel);
            statusLabel = null;
        }
    }
}