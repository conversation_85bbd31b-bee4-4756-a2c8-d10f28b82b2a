package cuc.states;

import cuc.gameObjects.Actor;
import cuc.utils.Animation2D;
import java.awt.Graphics;
import java.awt.event.MouseEvent;

public class TradeState extends ActorState {
    private String[] goods = {"药水", "剑", "盾"};
    private int[] prices = {10, 50, 30};
    private int selected = -1;
    public TradeState(Actor host, float maxLifeTime, int speed, Animation2D animation) {
        super(host, maxLifeTime, speed, animation, "");
        this.name = "trade";
    }
    @Override
    public void render(Graphics g) {
        super.render(g);
        // 显示交易界面
        int x = host.getPos().x;
        int y = host.getPos().y - host.getSize().y/2 - 100;
        g.setColor(java.awt.Color.WHITE);
        g.fillRect(x-80, y, 160, 100);
        g.setColor(java.awt.Color.BLACK);
        g.drawRect(x-80, y, 160, 100);
        g.drawString("请选择购买：", x-70, y+20);
        for (int i = 0; i < goods.length; i++) {
            g.drawString(goods[i] + " - " + prices[i] + "金币", x-70, y+40+20*i);
        }
        if (selected >= 0) {
            g.setColor(java.awt.Color.RED);
            g.drawString("已购买：" + goods[selected], x-70, y+90);
        }
    }
    // 简单处理：鼠标点击选择商品
    public void mouseReleased(MouseEvent e) {
        int x = host.getPos().x;
        int y = host.getPos().y - host.getSize().y/2 - 100;
        int mx = e.getX(), my = e.getY();
        for (int i = 0; i < goods.length; i++) {
            if (mx >= x-80 && mx <= x+80 && my >= y+25+20*i && my <= y+40+20*i) {
                selected = i;
            }
        }
    }
} 